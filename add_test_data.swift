#!/usr/bin/env swift

import Foundation
import SwiftData

// 定义 PasswordEntry 模型
@Model
final class PasswordEntry: Identifiable {
    @Attribute(.unique) var name: String
    var username: String
    var password: String
    var dateAdded: Date
    
    init(name: String, username: String, password: String) {
        self.name = name
        self.username = username
        self.password = password
        self.dateAdded = Date()
    }
}

// 创建 ModelContainer 并添加测试数据
@MainActor
func addTestData() async {
    let schema = Schema([PasswordEntry.self])
    let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

    do {
        let container = try ModelContainer(for: schema, configurations: [modelConfiguration])
        let context = container.mainContext
        
        // 添加一些测试数据
        let testEntries = [
            PasswordEntry(name: "GitHub", username: "<EMAIL>", password: "github123"),
            PasswordEntry(name: "G<PERSON>", username: "<EMAIL>", password: "gmail456"),
            PasswordEntry(name: "Apple ID", username: "<EMAIL>", password: "apple789"),
            PasswordEntry(name: "微信", username: "mingeme", password: "wechat123"),
            PasswordEntry(name: "支付宝", username: "<EMAIL>", password: "alipay456")
        ]
        
        for entry in testEntries {
            context.insert(entry)
        }
        
        try context.save()
        print("测试数据添加成功！")
        
    } catch {
        print("添加测试数据失败：\(error)")
    }
}

Task {
    await addTestData()
}
