import SwiftUI
import SwiftData

struct AccountListView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var allEntries: [PasswordEntry]
    @ObservedObject private var appState = AppState.shared
    
    private var filteredEntries: [PasswordEntry] {
        if appState.searchText.isEmpty {
            return allEntries.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }
        } else {
            return allEntries.filter { entry in
                entry.name.localizedCaseInsensitiveContains(appState.searchText) ||
                entry.username.localizedCaseInsensitiveContains(appState.searchText)
            }.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if filteredEntries.isEmpty {
                if appState.searchText.isEmpty {
                    EmptyStateView(
                        icon: "key.fill",
                        title: "暂无账号",
                        subtitle: "点击设置添加第一个账号"
                    )
                } else {
                    EmptyStateView(
                        icon: "magnifyingglass",
                        title: "未找到匹配的账号",
                        subtitle: "尝试使用其他关键词搜索"
                    )
                }
            } else {
                ScrollView {
                    LazyVStack(spacing: 2) {
                        ForEach(filteredEntries) { entry in
                            AccountRowView(entry: entry)
                        }
                    }
                    .padding(.vertical, 4)
                }
            }
        }
    }
}



#Preview {
    AccountListView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
        .frame(width: 280, height: 300)
}
