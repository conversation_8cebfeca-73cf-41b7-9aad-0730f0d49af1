import SwiftUI

struct HeaderView: View {
    var body: some View {
        HStack {
            Image(systemName: "lock.shield.fill")
                .font(.system(size: 16))
                .foregroundColor(.blue)
            
            Text("Passless")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("密码管理器")
                .font(.system(size: 11))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
    }
}
