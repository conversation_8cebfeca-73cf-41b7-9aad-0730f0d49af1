import SwiftUI
import AppKit
import SwiftData

class StatusBarController: NSObject {
    private var statusBar: NSStatusBar
    private var statusItem: NSStatusItem
    private var floatingWindow: FloatingWindow?
    private var eventMonitor: EventMonitor?
    private var searchField: NSSearchField?

    init(contentView: AnyView, modelContainer: ModelContainer) {
        statusBar = NSStatusBar.system
        statusItem = statusBar.statusItem(withLength: NSStatusItem.squareLength)

        super.init()

        if let statusBarButton = statusItem.button {
            statusBarButton.image = NSImage(systemSymbolName: "key.fill", accessibilityDescription: "Passless")
            statusBarButton.action = #selector(toggleWindow(_:))
            statusBarButton.target = self
        }

        // 创建浮动窗口
        self.floatingWindow = FloatingWindow(contentView: contentView, modelContainer: modelContainer)

        // 事件监听器，当点击其他地方时关闭窗口
        eventMonitor = EventMonitor(mask: [.leftMouseDown, .rightMouseDown]) { [weak self] event in
            guard let self = self, let window = self.floatingWindow?.window, window.isVisible else { return }
            self.closeWindow()
        }
    }

    @objc func toggleWindow(_ sender: AnyObject?) {
        guard let window = floatingWindow?.window else { return }

        if window.isVisible {
            closeWindow()
        } else {
            showWindow()
        }
    }

    func showWindow() {
        guard let statusBarButton = statusItem.button,
              let window = floatingWindow?.window else { return }

        // 计算窗口位置（在状态栏按钮下方）
        let buttonFrame = statusBarButton.convert(statusBarButton.bounds, to: nil)
        let screenFrame = statusBarButton.window?.convertToScreen(buttonFrame) ?? .zero

        let windowSize = window.frame.size
        let xPosition = screenFrame.midX - windowSize.width / 2
        let yPosition = screenFrame.minY - windowSize.height - 5

        window.setFrameOrigin(NSPoint(x: xPosition, y: yPosition))
        window.makeKeyAndOrderFront(nil)
        eventMonitor?.start()
    }

    func closeWindow() {
        floatingWindow?.close()
        eventMonitor?.stop()
    }
}

// 事件监听类
class EventMonitor {
    private var monitor: Any?
    private let mask: NSEvent.EventTypeMask
    private let handler: (NSEvent?) -> Void
    
    init(mask: NSEvent.EventTypeMask, handler: @escaping (NSEvent?) -> Void) {
        self.mask = mask
        self.handler = handler
    }
    
    deinit {
        stop()
    }
    
    func start() {
        monitor = NSEvent.addGlobalMonitorForEvents(matching: mask, handler: handler)
    }
    
    func stop() {
        if monitor != nil {
            NSEvent.removeMonitor(monitor!)
            monitor = nil
        }
    }
}
