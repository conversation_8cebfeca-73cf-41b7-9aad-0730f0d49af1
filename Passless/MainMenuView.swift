import SwiftUI

struct MainMenuView: View {
    @ObservedObject private var appState = AppState.shared
    @Binding var showSettings: Bool

    var body: some View {
        VStack(spacing: 0) {
            // 头部区域
            HeaderView()

            Divider()

            // 搜索栏
            VStack(spacing: 0) {
                SearchBarView()
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)

                Divider()
            }

            // 账号列表
            AccountListView()
                .frame(minHeight: 200, maxHeight: 300)

            Divider()

            // 底部操作栏
            FooterView(showSettings: $showSettings)
        }
    }
}
