import SwiftUI
import AppKit
import SwiftData

class FloatingWindow: NSObject {
    var window: NSWindow?
    private var hostingController: NSHostingController<AnyView>?
    
    init(contentView: AnyView, modelContainer: ModelContainer) {
        super.init()
        setupWindow(contentView: contentView, modelContainer: modelContainer)
    }
    
    private func setupWindow(contentView: AnyView, modelContainer: ModelContainer) {
        // 创建窗口
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 280, height: 400),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        guard let window = window else { return }
        
        // 窗口配置
        window.level = .floating
        window.backgroundColor = NSColor.clear
        window.isOpaque = false
        window.hasShadow = true
        window.isMovableByWindowBackground = false
        window.collectionBehavior = [.canJoinAllSpaces, .stationary]
        
        // 创建带有模型容器的内容视图
        let viewWithContainer = AnyView(contentView
            .modelContainer(modelContainer))

        // 创建 hosting controller
        hostingController = NSHostingController(rootView: viewWithContainer)
        window.contentViewController = hostingController
        
        // 设置窗口代理
        window.delegate = self
    }
    
    func close() {
        window?.orderOut(nil)
        // 确保焦点返回到之前的应用
        resignKey()
    }
    
    private func resignKey() {
        // 让窗口失去焦点，这样焦点就会返回到之前的应用
        window?.resignKey()
        
        // 额外确保应用失去激活状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            NSApp.hide(nil)
        }
    }
}

// MARK: - NSWindowDelegate
extension FloatingWindow: NSWindowDelegate {
    func windowDidResignKey(_ notification: Notification) {
        // 当窗口失去焦点时自动关闭
        close()
    }
    
    func windowWillClose(_ notification: Notification) {
        // 窗口即将关闭时的清理工作
        resignKey()
    }
}
