import SwiftUI

struct InlineSettingsView: View {
    @Binding var showSettings: Bool
    @AppStorage("launchAtLogin") private var launchAtLogin = false
    @AppStorage("autoHideMenu") private var autoHideMenu = true
    @AppStorage("pasteDelay") private var pasteDelay = 0.1

    var body: some View {
        VStack(spacing: 0) {
            // 头部
            HStack {
                Button(action: {
                    showSettings = false
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                }
                .buttonStyle(.plain)

                Spacer()

                Text("设置")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()

                // 占位符保持居中
                Color.clear
                    .frame(width: 20, height: 20)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)

            Divider()

            ScrollView {
                VStack(spacing: 0) {
                    // 开机自启动
                    SettingRowView(
                        title: "开机自启动",
                        subtitle: "系统启动时自动运行",
                        content: {
                            Toggle("", isOn: $launchAtLogin)
                                .toggleStyle(.switch)
                                .scaleEffect(0.8)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 自动隐藏菜单
                    SettingRowView(
                        title: "自动隐藏菜单",
                        subtitle: "粘贴完成后自动隐藏",
                        content: {
                            Toggle("", isOn: $autoHideMenu)
                                .toggleStyle(.switch)
                                .scaleEffect(0.8)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 粘贴延迟
                    SettingRowView(
                        title: "粘贴延迟",
                        subtitle: String(format: "%.2fs", pasteDelay),
                        content: {
                            Slider(value: $pasteDelay, in: 0.05...0.5, step: 0.05)
                                .frame(width: 80)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 关于信息
                    VStack(spacing: 8) {
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Passless v1.0.0")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.primary)

                                Text("安全的密码管理器")
                                    .font(.system(size: 10))
                                    .foregroundColor(.secondary)
                            }

                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 12)
                    }
                }
            }
            .frame(minHeight: 200, maxHeight: 300)
        }
    }
}
