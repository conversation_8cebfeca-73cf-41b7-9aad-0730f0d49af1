import SwiftUI

struct SearchBarView: View {
  @ObservedObject private var appState = AppState.shared

  var body: some View {
    HStack {
      Image(systemName: "magnifyingglass")
        .foregroundColor(.secondary)
        .font(.system(size: 12))

      TextField("搜索账号...", text: $appState.searchText)
        .textFieldStyle(.plain)
        .font(.system(size: 12))

      if !appState.searchText.isEmpty {
        Button(action: {
          appState.clearSearch()
        }) {
          Image(systemName: "xmark.circle.fill")
            .foregroundColor(.secondary)
            .font(.system(size: 10))
        }
        .buttonStyle(.plain)
      }
    }
    .padding(.horizontal, 8)
    .padding(.vertical, 6)
    .background(Color(NSColor.controlBackgroundColor))
    .cornerRadius(6)
  }
}
