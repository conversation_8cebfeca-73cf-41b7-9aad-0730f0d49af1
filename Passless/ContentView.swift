import SwiftUI
import SwiftData

struct ContentView: View {
    @ObservedObject private var appState = AppState.shared
    @State private var showSettings = false

    var body: some View {
        VStack(spacing: 0) {
            if showSettings {
                // 设置视图
                InlineSettingsView(showSettings: $showSettings)
            } else {
                // 主视图
                MainMenuView(showSettings: $showSettings)
            }
        }
        .frame(width: 280)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

#Preview {
    ContentView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
}
