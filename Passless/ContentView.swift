import SwiftUI
import SwiftData

struct ContentView: View {
    @ObservedObject private var appState = AppState.shared
    @State private var showSettings = false

    var body: some View {
        VStack(spacing: 0) {
            if showSettings {
                // 设置视图
                InlineSettingsView(showSettings: $showSettings)
            } else {
                // 主视图
                MainMenuView(showSettings: $showSettings)
            }
        }
        .frame(width: 280)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

struct MainMenuView: View {
    @ObservedObject private var appState = AppState.shared
    @Binding var showSettings: Bool

    var body: some View {
        VStack(spacing: 0) {
            // 头部区域
            HeaderView()

            Divider()

            // 搜索栏
            VStack(spacing: 0) {
                SearchBarView()
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)

                Divider()
            }

            // 账号列表
            AccountListView()
                .frame(minHeight: 200, maxHeight: 300)

            Divider()

            // 底部操作栏
            FooterView(showSettings: $showSettings)
        }
    }
}

struct HeaderView: View {
    var body: some View {
        HStack {
            Image(systemName: "lock.shield.fill")
                .font(.system(size: 16))
                .foregroundColor(.blue)
            
            Text("Passless")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
            
            Spacer()
            
            Text("密码管理器")
                .font(.system(size: 11))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
    }
}

struct FooterView: View {
    @ObservedObject private var appState = AppState.shared
    @Binding var showSettings: Bool

    var body: some View {
        HStack {
            Button(action: {
                showSettings = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "gearshape.fill")
                        .font(.system(size: 11))
                    Text("设置")
                        .font(.system(size: 11))
                }
                .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
            .padding(.vertical, 4)
            .padding(.horizontal, 6)
            .background(Color.clear)
            .cornerRadius(4)

            Spacer()

            Button(action: {
                NSApplication.shared.terminate(nil)
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "power")
                        .font(.system(size: 11))
                    Text("退出")
                        .font(.system(size: 11))
                }
                .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
            .padding(.vertical, 4)
            .padding(.horizontal, 6)
            .background(Color.clear)
            .cornerRadius(4)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

struct InlineSettingsView: View {
    @Binding var showSettings: Bool
    @AppStorage("launchAtLogin") private var launchAtLogin = false
    @AppStorage("autoHideMenu") private var autoHideMenu = true
    @AppStorage("pasteDelay") private var pasteDelay = 0.1

    var body: some View {
        VStack(spacing: 0) {
            // 头部
            HStack {
                Button(action: {
                    showSettings = false
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                }
                .buttonStyle(.plain)

                Spacer()

                Text("设置")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()

                // 占位符保持居中
                Color.clear
                    .frame(width: 20, height: 20)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)

            Divider()

            ScrollView {
                VStack(spacing: 0) {
                    // 开机自启动
                    SettingRowView(
                        title: "开机自启动",
                        subtitle: "系统启动时自动运行",
                        content: {
                            Toggle("", isOn: $launchAtLogin)
                                .toggleStyle(.switch)
                                .scaleEffect(0.8)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 自动隐藏菜单
                    SettingRowView(
                        title: "自动隐藏菜单",
                        subtitle: "粘贴完成后自动隐藏",
                        content: {
                            Toggle("", isOn: $autoHideMenu)
                                .toggleStyle(.switch)
                                .scaleEffect(0.8)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 粘贴延迟
                    SettingRowView(
                        title: "粘贴延迟",
                        subtitle: String(format: "%.2fs", pasteDelay),
                        content: {
                            Slider(value: $pasteDelay, in: 0.05...0.5, step: 0.05)
                                .frame(width: 80)
                        }
                    )

                    Divider()
                        .padding(.leading, 12)

                    // 关于信息
                    VStack(spacing: 8) {
                        HStack {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("Passless v1.0.0")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.primary)

                                Text("安全的密码管理器")
                                    .font(.system(size: 10))
                                    .foregroundColor(.secondary)
                            }

                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 12)
                    }
                }
            }
            .frame(minHeight: 200, maxHeight: 300)
        }
    }
}

struct SettingRowView<Content: View>: View {
    let title: String
    let subtitle: String
    let content: () -> Content

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
            }

            Spacer()

            content()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

#Preview {
    ContentView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
}
