import Foundation
import AppKit

@MainActor
class PasteService: ObservableObject {
    static let shared = PasteService()

    private init() {}

    func pasteCredentials(username: String, password: String) {
        // 检查辅助功能权限
        guard checkAccessibilityPermissions() else {
            showAccessibilityAlert()
            return
        }

        // 获取当前前台应用（在 Passless 激活之前的应用）
        let targetApp = getPreviousFrontmostApplication()

        // 关闭菜单
        NotificationCenter.default.post(name: .closeStatusMenu, object: nil)

        // 等待菜单关闭，然后执行输入
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.performInputWithFocusManagement(
                targetApp: targetApp,
                username: username,
                password: password
            )
        }
    }

    // 获取之前的前台应用（排除 Passless 自己）
    private func getPreviousFrontmostApplication() -> NSRunningApplication? {
        let runningApps = NSWorkspace.shared.runningApplications

        // 查找最近激活的非 Passless 应用
        for app in runningApps {
            if app.bundleIdentifier != Bundle.main.bundleIdentifier &&
               app.activationPolicy == .regular &&
               !app.isTerminated {
                return app
            }
        }

        return nil
    }

    private func performInputWithFocusManagement(targetApp: NSRunningApplication?, username: String, password: String) {
        guard let app = targetApp else {
            print("没有找到目标应用")
            return
        }

        print("准备激活目标应用: \(app.localizedName ?? "Unknown")")

        // 强制激活目标应用
        if #available(macOS 14.0, *) {
            app.activate()
        } else {
            app.activate(options: [.activateIgnoringOtherApps])
        }

        // 等待应用完全激活后执行输入
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 再次确认应用已激活
            if app.isActive {
                print("目标应用已激活，开始输入")
                self.performPasteSequence(username: username, password: password)
            } else {
                print("应用激活失败，尝试备用方法")
                // 使用 AppleScript 强制激活
                self.forceActivateApp(app) {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        self.performPasteSequence(username: username, password: password)
                    }
                }
            }
        }
    }

    // 使用 AppleScript 强制激活应用
    private func forceActivateApp(_ app: NSRunningApplication, completion: @escaping () -> Void) {
        guard let bundleId = app.bundleIdentifier else {
            completion()
            return
        }

        let script = """
        tell application id "\(bundleId)"
            activate
        end tell
        """

        let appleScript = NSAppleScript(source: script)
        appleScript?.executeAndReturnError(nil)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            completion()
        }
    }

    private func performPasteSequence(username: String, password: String) {
        // 获取用户设置的延迟时间
        let pasteDelay = UserDefaults.standard.double(forKey: "pasteDelay")
        let delay = pasteDelay > 0 ? pasteDelay : 0.2

        // 1. 输入用户名
        self.typeText(username)

        // 2. 等待后按Tab键
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            self.pressTab()

            // 3. 再等待后输入密码
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                self.typeText(password)
            }
        }
    }





    // 检查辅助功能权限
    private func checkAccessibilityPermissions() -> Bool {
        let checkOptPrompt = kAXTrustedCheckOptionPrompt.takeUnretainedValue() as NSString
        let options = [checkOptPrompt: false]
        return AXIsProcessTrustedWithOptions(options as CFDictionary)
    }

    // 显示权限提示
    private func showAccessibilityAlert() {
        let alert = NSAlert()
        alert.messageText = "需要辅助功能权限"
        alert.informativeText = "Passless 需要辅助功能权限才能自动输入用户名和密码。请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加 Passless。"
        alert.alertStyle = .warning
        alert.addButton(withTitle: "打开系统偏好设置")
        alert.addButton(withTitle: "取消")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            NSWorkspace.shared.open(URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!)
        }
    }

    // 直接输入文本（更可靠的方法）
    private func typeText(_ text: String) {
        guard let source = CGEventSource(stateID: .hidSystemState) else { return }

        for character in text {
            if let keyCode = getKeyCode(for: character) {
                // 处理需要 Shift 的字符
                let needsShift = needsShiftKey(for: character)

                if needsShift {
                    // 按下 Shift
                    let shiftDown = CGEvent(keyboardEventSource: source, virtualKey: 0x38, keyDown: true)
                    shiftDown?.post(tap: .cghidEventTap)
                }

                // 按下字符键
                let keyDown = CGEvent(keyboardEventSource: source, virtualKey: keyCode, keyDown: true)
                let keyUp = CGEvent(keyboardEventSource: source, virtualKey: keyCode, keyDown: false)

                if needsShift {
                    keyDown?.flags = .maskShift
                    keyUp?.flags = .maskShift
                }

                keyDown?.post(tap: .cghidEventTap)
                Thread.sleep(forTimeInterval: 0.01) // 短暂延迟
                keyUp?.post(tap: .cghidEventTap)

                if needsShift {
                    // 释放 Shift
                    let shiftUp = CGEvent(keyboardEventSource: source, virtualKey: 0x38, keyDown: false)
                    shiftUp?.post(tap: .cghidEventTap)
                }

                Thread.sleep(forTimeInterval: 0.01) // 字符间延迟
            }
        }
    }

    private func pressTab() {
        guard let source = CGEventSource(stateID: .hidSystemState) else { return }

        let tabDown = CGEvent(keyboardEventSource: source, virtualKey: 0x30, keyDown: true)
        let tabUp = CGEvent(keyboardEventSource: source, virtualKey: 0x30, keyDown: false)

        tabDown?.post(tap: .cghidEventTap)
        Thread.sleep(forTimeInterval: 0.05)
        tabUp?.post(tap: .cghidEventTap)
    }

    // 获取字符对应的键盘码
    private func getKeyCode(for character: Character) -> CGKeyCode? {
        let keyMap: [Character: CGKeyCode] = [
            "a": 0x00, "b": 0x0B, "c": 0x08, "d": 0x02, "e": 0x0E, "f": 0x03, "g": 0x05,
            "h": 0x04, "i": 0x22, "j": 0x26, "k": 0x28, "l": 0x25, "m": 0x2E, "n": 0x2D,
            "o": 0x1F, "p": 0x23, "q": 0x0C, "r": 0x0F, "s": 0x01, "t": 0x11, "u": 0x20,
            "v": 0x09, "w": 0x0D, "x": 0x07, "y": 0x10, "z": 0x06,

            "A": 0x00, "B": 0x0B, "C": 0x08, "D": 0x02, "E": 0x0E, "F": 0x03, "G": 0x05,
            "H": 0x04, "I": 0x22, "J": 0x26, "K": 0x28, "L": 0x25, "M": 0x2E, "N": 0x2D,
            "O": 0x1F, "P": 0x23, "Q": 0x0C, "R": 0x0F, "S": 0x01, "T": 0x11, "U": 0x20,
            "V": 0x09, "W": 0x0D, "X": 0x07, "Y": 0x10, "Z": 0x06,

            "0": 0x1D, "1": 0x12, "2": 0x13, "3": 0x14, "4": 0x15, "5": 0x17,
            "6": 0x16, "7": 0x1A, "8": 0x1C, "9": 0x19,

            ")": 0x1D, "!": 0x12, "@": 0x13, "#": 0x14, "$": 0x15, "%": 0x17,
            "^": 0x16, "&": 0x1A, "*": 0x1C, "(": 0x19,

            " ": 0x31, "-": 0x1B, "=": 0x18, "[": 0x21, "]": 0x1E, "\\": 0x2A,
            ";": 0x29, "'": 0x27, ",": 0x2B, ".": 0x2F, "/": 0x2C,
            "_": 0x1B, "+": 0x18, "{": 0x21, "}": 0x1E, "|": 0x2A,
            ":": 0x29, "\"": 0x27, "<": 0x2B, ">": 0x2F, "?": 0x2C,
            "`": 0x32, "~": 0x32
        ]

        return keyMap[character]
    }

    // 判断字符是否需要 Shift 键
    private func needsShiftKey(for character: Character) -> Bool {
        let shiftCharacters: Set<Character> = [
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
            "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
            "!", "@", "#", "$", "%", "^", "&", "*", "(", ")",
            "_", "+", "{", "}", "|", ":", "\"", "<", ">", "?", "~"
        ]
        return shiftCharacters.contains(character)
    }

    // 备用方法：使用剪贴板粘贴（如果直接输入失败）
    private func pasteText(_ text: String) {
        let pasteboard = NSPasteboard.general
        let originalContents = pasteboard.string(forType: .string) // 保存原始剪贴板内容

        pasteboard.clearContents()
        pasteboard.setString(text, forType: .string)

        guard let source = CGEventSource(stateID: .hidSystemState) else { return }

        // 模拟 Cmd+V
        let cmdDown = CGEvent(keyboardEventSource: source, virtualKey: 0x37, keyDown: true)
        let vDown = CGEvent(keyboardEventSource: source, virtualKey: 0x09, keyDown: true)
        let vUp = CGEvent(keyboardEventSource: source, virtualKey: 0x09, keyDown: false)
        let cmdUp = CGEvent(keyboardEventSource: source, virtualKey: 0x37, keyDown: false)

        cmdDown?.flags = .maskCommand
        vDown?.flags = .maskCommand

        cmdDown?.post(tap: .cghidEventTap)
        vDown?.post(tap: .cghidEventTap)
        Thread.sleep(forTimeInterval: 0.05)
        vUp?.post(tap: .cghidEventTap)
        cmdUp?.post(tap: .cghidEventTap)

        // 延迟后恢复原始剪贴板内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if let originalContents = originalContents {
                pasteboard.clearContents()
                pasteboard.setString(originalContents, forType: .string)
            }
        }
    }

    // 提供一个使用粘贴方式的备用方法
    func pasteCredentialsUsingClipboard(username: String, password: String) {
        // 检查辅助功能权限
        guard checkAccessibilityPermissions() else {
            showAccessibilityAlert()
            return
        }

        // 获取当前前台应用（在 Passless 激活之前的应用）
        let targetApp = getPreviousFrontmostApplication()

        // 关闭菜单
        NotificationCenter.default.post(name: .closeStatusMenu, object: nil)

        // 等待菜单关闭后执行粘贴
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.performClipboardWithFocusManagement(
                targetApp: targetApp,
                username: username,
                password: password
            )
        }
    }

    private func performClipboardWithFocusManagement(targetApp: NSRunningApplication?, username: String, password: String) {
        guard let app = targetApp else {
            print("没有找到目标应用")
            return
        }

        print("准备激活目标应用: \(app.localizedName ?? "Unknown")")

        // 强制激活目标应用
        if #available(macOS 14.0, *) {
            app.activate()
        } else {
            app.activate(options: [.activateIgnoringOtherApps])
        }

        // 等待应用完全激活后执行粘贴
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // 再次确认应用已激活
            if app.isActive {
                print("目标应用已激活，开始粘贴")
                self.performClipboardPasteSequence(username: username, password: password)
            } else {
                print("应用激活失败，尝试备用方法")
                // 使用 AppleScript 强制激活
                self.forceActivateApp(app) {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                        self.performClipboardPasteSequence(username: username, password: password)
                    }
                }
            }
        }
    }

    private func performClipboardPasteSequence(username: String, password: String) {
        let pasteDelay = UserDefaults.standard.double(forKey: "pasteDelay")
        let delay = pasteDelay > 0 ? pasteDelay : 0.2

        // 1. 粘贴用户名
        self.pasteText(username)

        // 2. 等待后按Tab键
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            self.pressTab()

            // 3. 再等待后粘贴密码
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                self.pasteText(password)
            }
        }
    }
}
