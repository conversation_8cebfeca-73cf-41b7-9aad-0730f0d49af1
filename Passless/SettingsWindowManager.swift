import SwiftUI
import AppKit

@MainActor
class SettingsWindowManager: ObservableObject {
    static let shared = SettingsWindowManager()

    fileprivate var settingsWindow: NSWindow?
    fileprivate var windowDelegate: SettingsWindowDelegate?

    private init() {}
    
    func showSettings() {
        if let existingWindow = settingsWindow {
            existingWindow.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            return
        }
        
        let contentView = SettingsWindowView()
        let hostingView = NSHostingView(rootView: contentView)
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 500),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "Passless 设置"
        window.contentView = hostingView
        window.center()
        window.setFrameAutosaveName("SettingsWindow")
        window.isReleasedWhenClosed = false
        
        // 设置窗口代理来处理关闭事件
        let delegate = SettingsWindowDelegate()
        windowDelegate = delegate
        window.delegate = delegate

        settingsWindow = window
        window.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func closeSettings() {
        settingsWindow?.close()
        settingsWindow = nil
        windowDelegate = nil
    }
}

class SettingsWindowDelegate: NSObject, NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        Task { @MainActor in
            SettingsWindowManager.shared.settingsWindow = nil
            SettingsWindowManager.shared.windowDelegate = nil
        }
    }
}
