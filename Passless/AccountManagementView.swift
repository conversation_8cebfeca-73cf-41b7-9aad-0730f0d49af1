import SwiftData
import SwiftUI

struct AccountManagementView: View {
  @Environment(\.modelContext) private var modelContext
  @Query private var entries: [PasswordEntry]
  @State private var showingAddAccount = false
  @State private var editingEntry: PasswordEntry?

  var body: some View {
    VStack(spacing: 0) {
      // 头部操作栏
      HStack {
        Text("账号管理")
          .font(.system(size: 16, weight: .semibold))

        Spacer()

        Button(action: {
          showingAddAccount = true
        }) {
          HStack(spacing: 4) {
            Image(systemName: "plus")
              .font(.system(size: 12))
            Text("添加账号")
              .font(.system(size: 12))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 12)
          .padding(.vertical, 6)
          .background(Color.blue)
          .cornerRadius(6)
        }
        .buttonStyle(.plain)
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 12)

      Divider()

      // 账号列表
      if entries.isEmpty {
        EmptyAccountsView()
      } else {
        ScrollView {
          LazyVStack(spacing: 8) {
            ForEach(
              entries.sorted {
                $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending
              }
            ) { entry in
              AccountManagementRowView(
                entry: entry,
                onEdit: { editingEntry = entry },
                onDelete: { deleteEntry(entry) }
              )
            }
          }
          .padding(.horizontal, 16)
          .padding(.vertical, 12)
        }
      }
    }
    .sheet(isPresented: $showingAddAccount) {
      AddAccountView()
    }
    .sheet(item: $editingEntry) { entry in
      EditAccountView(entry: entry)
    }
  }

  private func deleteEntry(_ entry: PasswordEntry) {
    withAnimation {
      modelContext.delete(entry)
      try? modelContext.save()
    }
  }
}

struct AccountManagementRowView: View {
  let entry: PasswordEntry
  let onEdit: () -> Void
  let onDelete: () -> Void
  @State private var isHovered = false
  @State private var showingDeleteAlert = false

  var body: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text(entry.name)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.primary)

        Text(entry.username)
          .font(.system(size: 12))
          .foregroundColor(.secondary)

        Text("添加于 \(entry.dateAdded.formatted(date: .abbreviated, time: .omitted))")
          .font(.system(size: 10))
          .foregroundColor(.secondary)
      }

      Spacer()

      if isHovered {
        HStack(spacing: 8) {
          Button(action: onEdit) {
            Image(systemName: "pencil")
              .font(.system(size: 12))
              .foregroundColor(.blue)
          }
          .buttonStyle(.plain)

          Button(action: {
            showingDeleteAlert = true
          }) {
            Image(systemName: "trash")
              .font(.system(size: 12))
              .foregroundColor(.red)
          }
          .buttonStyle(.plain)
        }
      }
    }
    .padding(.horizontal, 12)
    .padding(.vertical, 10)
    .background(isHovered ? Color(NSColor.controlBackgroundColor) : Color.clear)
    .cornerRadius(8)
    .onHover { hover in
      withAnimation(.easeInOut(duration: 0.1)) {
        isHovered = hover
      }
    }
    .alert("删除账号", isPresented: $showingDeleteAlert) {
      Button("取消", role: .cancel) {}
      Button("删除", role: .destructive) {
        onDelete()
      }
    } message: {
      Text("确定要删除账号 \"\(entry.name)\" 吗？此操作无法撤销。")
    }
  }
}

struct EmptyAccountsView: View {
  var body: some View {
    VStack(spacing: 16) {
      Image(systemName: "person.2.slash")
        .font(.system(size: 48))
        .foregroundColor(.secondary)

      VStack(spacing: 8) {
        Text("暂无账号")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.primary)

        Text("点击上方的“添加账号”按钮来创建第一个账号")
          .font(.system(size: 14))
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .padding()
  }
}

#Preview {
  AccountManagementView()
    .modelContainer(for: PasswordEntry.self, inMemory: true)
    .frame(width: 500, height: 400)
}
