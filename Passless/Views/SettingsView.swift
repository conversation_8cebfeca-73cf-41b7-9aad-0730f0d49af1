import SwiftUI
import SwiftData

struct SettingsView: View {
    @State private var selectedTab = "general"

    var body: some View {
        TabView(selection: $selectedTab) {
            GeneralSettingsView()
                .tabItem {
                    Label("通用", systemImage: "gearshape")
                }
                .tag("general")

            AccountsSettingsView()
                .tabItem {
                    Label("账号", systemImage: "person.circle")
                }
                .tag("accounts")
        }
        .frame(
            width: selectedTab == "general" ? 300 : 800,
            height: selectedTab == "general" ? 200 : 600
        )
        .animation(.easeInOut(duration: 0.2), value: selectedTab)
    }
}

#Preview {
    SettingsView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
}
