import SwiftUI
import SwiftData

struct SettingsView: View {
    var body: some View {
        TabView {
            GeneralSettingsView()
                .tabItem {
                    Label("通用", systemImage: "gearshape")
                }
                .tag("general")

            AccountsSettingsView()
                .tabItem {
                    Label("账号", systemImage: "person.circle")
                }
                .tag("accounts")
        }
        .frame(width: 600, height: 500)
    }
}

struct GeneralSettingsView: View {
    @State private var launchAtLogin = LaunchAtLoginManager.shared.isEnabled

    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }

    var body: some View {
        Form {
            Section("启动") {
                Toggle("登录时启动", isOn: $launchAtLogin)
                    .onChange(of: launchAtLogin) { _, newValue in
                        LaunchAtLoginManager.shared.isEnabled = newValue
                    }
            }

            Section("关于") {
                HStack {
                    Text("版本:")
                    Spacer()
                    Text(appVersion)
                        .foregroundColor(.secondary)
                }
            }
        }
        .formStyle(.grouped)
        .padding()
        .onAppear {
            launchAtLogin = LaunchAtLoginManager.shared.isEnabled
        }
    }
}

struct AccountsSettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var accounts: [PasswordEntry]
    @State private var showingAddAccount = false
    @State private var selectedAccount: PasswordEntry?
    @State private var showingEditAccount = false
    @State private var selectedAccountIDs = Set<PasswordEntry.ID>()

    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            HStack {
                Button(action: { showingAddAccount = true }) {
                    Image(systemName: "plus")
                        .font(.system(size: 14))
                }
                .buttonStyle(.borderless)
                .help("添加账号")

                Button(action: deleteSelectedAccounts) {
                    Image(systemName: "minus")
                        .font(.system(size: 14))
                }
                .buttonStyle(.borderless)
                .disabled(selectedAccountIDs.isEmpty)
                .help("删除选中账号")

                Button(action: editSelectedAccount) {
                    Text("Edit")
                        .font(.system(size: 12))
                }
                .buttonStyle(.borderless)
                .disabled(selectedAccountIDs.count != 1)
                .help("编辑账号")

                Spacer()

                // 导入导出按钮
                Button(action: {}) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 14))
                }
                .buttonStyle(.borderless)
                .help("导出")

                Button(action: {}) {
                    Image(systemName: "square.and.arrow.down")
                        .font(.system(size: 14))
                }
                .buttonStyle(.borderless)
                .help("导入")
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))

            // 表格
            if accounts.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)

                    Text("暂无账号")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text("点击上方 + 按钮添加您的第一个账号")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            } else {
                AccountTableView(
                    accounts: accounts,
                    selectedAccountIDs: $selectedAccountIDs,
                    onEdit: { account in
                        selectedAccount = account
                        showingEditAccount = true
                    }
                )
            }
        }
        .sheet(isPresented: $showingAddAccount) {
            AddAccountView()
        }
        .sheet(isPresented: $showingEditAccount) {
            if let account = selectedAccount {
                EditAccountView(entry: account)
            }
        }
    }

    private func deleteSelectedAccounts() {
        let accountsToDelete = accounts.filter { selectedAccountIDs.contains($0.id) }
        withAnimation {
            for account in accountsToDelete {
                modelContext.delete(account)
            }
            selectedAccountIDs.removeAll()
        }
    }

    private func editSelectedAccount() {
        if selectedAccountIDs.count == 1,
           let accountID = selectedAccountIDs.first,
           let account = accounts.first(where: { $0.id == accountID }) {
            selectedAccount = account
            showingEditAccount = true
        }
    }
}

struct AccountTableView: View {
    let accounts: [PasswordEntry]
    @Binding var selectedAccountIDs: Set<PasswordEntry.ID>
    let onEdit: (PasswordEntry) -> Void

    var body: some View {
        VStack(spacing: 0) {
            // 表头
            HStack(spacing: 0) {
                Text("Title")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)

                Divider()
                    .frame(height: 20)

                Text("Action")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 100, alignment: .leading)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
            }
            .background(Color(NSColor.controlBackgroundColor))

            Divider()

            // 表格内容
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(accounts) { account in
                        AccountTableRowView(
                            account: account,
                            isSelected: selectedAccountIDs.contains(account.id),
                            onSelectionChanged: { isSelected in
                                if isSelected {
                                    selectedAccountIDs.insert(account.id)
                                } else {
                                    selectedAccountIDs.remove(account.id)
                                }
                            },
                            onEdit: { onEdit(account) }
                        )

                        if account.id != accounts.last?.id {
                            Divider()
                                .padding(.leading, 12)
                        }
                    }
                }
            }
            .background(Color(NSColor.textBackgroundColor))
        }
    }
}

struct AccountTableRowView: View {
    let account: PasswordEntry
    let isSelected: Bool
    let onSelectionChanged: (Bool) -> Void
    let onEdit: () -> Void
    @State private var isHovered = false

    var body: some View {
        HStack(spacing: 0) {
            // Title 列
            HStack(spacing: 8) {
                Button(action: { onSelectionChanged(!isSelected) }) {
                    Image(systemName: isSelected ? "checkmark.square.fill" : "square")
                        .font(.system(size: 14))
                        .foregroundColor(isSelected ? .accentColor : .secondary)
                }
                .buttonStyle(.plain)

                VStack(alignment: .leading, spacing: 2) {
                    Text(account.name)
                        .font(.system(size: 13))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    if !account.username.isEmpty {
                        Text(account.username)
                            .font(.system(size: 11))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }

                Spacer()
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)

            // Action 列
            HStack(spacing: 4) {
                if isHovered {
                    Button("Edit") {
                        onEdit()
                    }
                    .font(.system(size: 11))
                    .buttonStyle(.borderless)
                }
            }
            .frame(width: 100, alignment: .leading)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        }
        .background(
            Rectangle()
                .fill(isSelected ? Color.accentColor.opacity(0.1) :
                      (isHovered ? Color.primary.opacity(0.05) : Color.clear))
        )
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            onSelectionChanged(!isSelected)
        }
    }
}

#Preview {
    SettingsView()
}
