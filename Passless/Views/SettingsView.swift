import SwiftUI
import SwiftData

struct SettingsView: View {
    var body: some View {
        TabView {
            GeneralSettingsView()
                .tabItem {
                    Label("通用", systemImage: "gearshape")
                }
                .tag("general")

            AccountsSettingsView()
                .tabItem {
                    Label("账号", systemImage: "person.circle")
                }
                .tag("accounts")
        }
        .frame(width: 800, height: 600)
    }
}

#Preview {
    SettingsView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
}
