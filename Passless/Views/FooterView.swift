import SwiftUI

struct FooterView: View {
    @ObservedObject private var appState = AppState.shared
    @Binding var showSettings: Bool

    var body: some View {
        HStack(spacing: 12) {
            Button(action: {
                showSettings = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "gearshape.fill")
                        .font(.system(size: 11))
                    Text("设置")
                        .font(.system(size: 11))
                }
                .foregroundColor(Color(NSColor.placeholderTextColor))
            }
            .buttonStyle(.plain)
            .padding(.vertical, 3)
            .padding(.horizontal, 6)
            .background(Color.clear)
            .cornerRadius(4)
            .onHover { isHovered in
                // 可以添加悬停效果
            }

            Spacer()

            But<PERSON>(action: {
                NSApplication.shared.terminate(nil)
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "power")
                        .font(.system(size: 11))
                    Text("退出")
                        .font(.system(size: 11))
                }
                .foregroundColor(Color(NSColor.placeholderTextColor))
            }
            .buttonStyle(.plain)
            .padding(.vertical, 3)
            .padding(.horizontal, 6)
            .background(Color.clear)
            .cornerRadius(4)
            .onHover { isHovered in
                // 可以添加悬停效果
            }
        }
    }
}
