import SwiftUI
import SwiftData

struct AccountManagementView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var entries: [PasswordEntry]
    @State private var showingAddAccount = false
    @State private var editingEntry: PasswordEntry?

    var body: some View {
        VStack(spacing: 0) {
            // 头部操作栏
            HStack {
                Text("账号管理")
                    .font(.system(size: 16, weight: .semibold))

                Spacer()

                Button(action: {
                    showingAddAccount = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus")
                            .font(.system(size: 12))
                        Text("添加账号")
                            .font(.system(size: 12))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue)
                    .cornerRadius(6)
                }
                .buttonStyle(.plain)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)

            Divider()

            // 账号列表
            if entries.isEmpty {
                EmptyAccountsView()
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(entries.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }) { entry in
                            AccountManagementRowView(
                                entry: entry,
                                onEdit: { editingEntry = entry },
                                onDelete: { deleteEntry(entry) }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                }
            }
        }
        .sheet(isPresented: $showingAddAccount) {
            AddAccountView()
        }
        .sheet(item: $editingEntry) { entry in
            EditAccountView(entry: entry)
        }
    }

    private func deleteEntry(_ entry: PasswordEntry) {
        withAnimation {
            modelContext.delete(entry)
            try? modelContext.save()
        }
    }
}

#Preview {
    AccountManagementView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
        .frame(width: 500, height: 400)
}