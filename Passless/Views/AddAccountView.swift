import SwiftUI
import SwiftData

struct AddAccountView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var name = ""
    @State private var username = ""
    @State private var password = ""
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 头部工具栏
            HStack {
                Button("取消") {
                    dismiss()
                }
                .buttonStyle(.plain)
                
                Spacer()
                
                Text("添加账号")
                    .font(.system(size: 16, weight: .semibold))
                
                Spacer()
                
                But<PERSON>("保存") {
                    saveAccount()
                }
                .buttonStyle(.plain)
                .disabled(name.isEmpty || username.isEmpty || password.isEmpty)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            
            Divider()
            
            // 表单内容
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("账号名称")
                            .font(.system(size: 14, weight: .medium))
                        TextField("例如：GitHub、Gmail 等", text: $name)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("用户名")
                            .font(.system(size: 14, weight: .medium))
                        TextField("邮箱或用户名", text: $username)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("密码")
                            .font(.system(size: 14, weight: .medium))
                        SecureField("密码", text: $password)
                            .textFieldStyle(.roundedBorder)
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
            .padding(.top, 20)
        }
        .frame(width: 400, height: 300)
        .alert("错误", isPresented: $showingError) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private func saveAccount() {
        // 检查账号名称是否已存在
        let descriptor = FetchDescriptor<PasswordEntry>(
            predicate: #Predicate { $0.name == name }
        )
        
        do {
            let existingEntries = try modelContext.fetch(descriptor)
            if !existingEntries.isEmpty {
                errorMessage = "账号名称已存在，请使用其他名称"
                showingError = true
                return
            }
            
            let newEntry = PasswordEntry(name: name, username: username, password: password)
            modelContext.insert(newEntry)
            try modelContext.save()
            dismiss()
        } catch {
            errorMessage = "保存失败：\(error.localizedDescription)"
            showingError = true
        }
    }
}

#Preview {
    AddAccountView()
        .modelContainer(for: PasswordEntry.self, inMemory: true)
}
