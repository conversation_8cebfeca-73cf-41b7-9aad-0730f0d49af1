import SwiftUI
import SwiftData

struct EditableAccountTableView: View {
    @Binding var editingAccounts: [EditableAccount]
    let onDelete: (EditableAccount) -> Void
    let onAdd: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // 表格内容
            if editingAccounts.isEmpty {
                // 空状态
                VStack(spacing: 16) {
                    Image(systemName: "person.circle")
                        .font(.system(size: 48))
                        .foregroundColor(Color.secondary)

                    Text("暂无账号")
                        .font(.title2)
                        .foregroundColor(Color.primary)

                    Text("点击下方 + 按钮添加您的第一个账号")
                        .font(.body)
                        .foregroundColor(Color.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .frame(minHeight: 200)
            } else {
                // 表格列表
                List {
                    ForEach(editingAccounts) { account in
                        EditableAccountRowView(account: account)
                            .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
                    }
                    .onDelete { indexSet in
                        for index in indexSet {
                            onDelete(editingAccounts[index])
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }

            // 底部操作栏
            HStack {
                Button(action: onAdd) {
                    Image(systemName: "plus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.primary)
                }
                .buttonStyle(PlainButtonStyle())
                .frame(width: 20, height: 20)

                Button(action: {
                    // 删除选中项的逻辑可以通过 List 的 onDelete 处理
                }) {
                    Image(systemName: "minus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(editingAccounts.isEmpty ? Color.secondary : Color.primary)
                }
                .buttonStyle(PlainButtonStyle())
                .frame(width: 20, height: 20)
                .disabled(editingAccounts.isEmpty)

                Spacer()
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(Color(NSColor.controlBackgroundColor))
            .border(Color(NSColor.separatorColor), width: 1)
        }
        .background(Color(NSColor.textBackgroundColor))
        .cornerRadius(6)
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(Color(NSColor.separatorColor), lineWidth: 1)
        )
    }
}

#Preview {
    @Previewable @State var accounts: [EditableAccount] = [
        EditableAccount(id: UUID(), index: 1, name: "测试账号", username: "<EMAIL>", password: "password123", originalAccount: nil)
    ]

    EditableAccountTableView(
        editingAccounts: $accounts,
        onDelete: { _ in },
        onAdd: { }
    )
    .frame(width: 800, height: 400)
}
