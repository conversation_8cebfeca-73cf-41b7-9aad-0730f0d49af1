import SwiftUI
import SwiftData

struct EditableAccountTableView: View {
    @Binding var editingAccounts: [EditableAccount]
    let onDelete: (EditableAccount) -> Void
    let onSave: (EditableAccount) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 表头
            HStack(spacing: 0) {
                Text("序号")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 60, alignment: .center)
                    .padding(.vertical, 12)
                
                Divider()
                    .frame(height: 20)
                
                Text("名称")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 12)
                
                Divider()
                    .frame(height: 20)
                
                Text("用户名")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 12)
                
                Divider()
                    .frame(height: 20)
                
                Text("密码")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 12)
                
                Divider()
                    .frame(height: 20)
                
                Text("操作")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(width: 80, alignment: .center)
                    .padding(.vertical, 12)
            }
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // 表格内容
            if editingAccounts.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("暂无账号")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("点击上方\"添加账号\"按钮创建您的第一个账号")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(editingAccounts) { account in
                            EditableAccountRowView(
                                account: account,
                                onDelete: { onDelete(account) },
                                onSave: { onSave(account) }
                            )
                            
                            if account.id != editingAccounts.last?.id {
                                Divider()
                                    .padding(.leading, 60)
                            }
                        }
                    }
                }
                .background(Color(NSColor.textBackgroundColor))
            }
        }
    }
}

#Preview {
    @Previewable @State var accounts: [EditableAccount] = [
        EditableAccount(id: UUID(), index: 1, name: "测试账号", username: "<EMAIL>", password: "password123", originalAccount: nil)
    ]
    
    EditableAccountTableView(
        editingAccounts: $accounts,
        onDelete: { _ in },
        onSave: { _ in }
    )
    .frame(width: 800, height: 400)
}
