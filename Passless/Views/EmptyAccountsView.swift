import SwiftUI

struct EmptyAccountsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.2.slash")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("暂无账号")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.primary)
                
                Text("点击上方的\"添加账号\"按钮来创建第一个账号")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}
