import SwiftUI

struct SettingRowView<Content: View>: View {
    let title: String
    let subtitle: String
    let content: () -> Content

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
            }

            Spacer()

            content()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}
