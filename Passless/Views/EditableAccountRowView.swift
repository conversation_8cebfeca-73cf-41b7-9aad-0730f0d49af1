import SwiftUI
import SwiftData

struct EditableAccountRowView: View {
    @ObservedObject var account: EditableAccount

    var body: some View {
        HStack(spacing: 12) {
            // 序号
            Text("\(account.index)")
                .font(.system(size: 13))
                .foregroundColor(Color.secondary)
                .frame(width: 40, alignment: .center)

            // 名称
            TextField("名称", text: $account.name)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
                .foregroundColor(Color.primary)

            // 用户名
            TextField("用户名", text: $account.username)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
                .foregroundColor(Color.primary)

            // 密码 (纯文本显示)
            TextField("密码", text: $account.password)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
                .foregroundColor(Color.primary)
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
    }
}

#Preview {
    let account = EditableAccount(
        id: UUID(),
        index: 1,
        name: "测试账号",
        username: "<EMAIL>",
        password: "password123",
        originalAccount: nil
    )

    EditableAccountRowView(account: account)
        .frame(width: 800, height: 50)
}
