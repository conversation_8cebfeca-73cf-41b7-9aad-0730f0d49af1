import SwiftUI
import SwiftData

struct EditableAccountRowView: View {
    @ObservedObject var account: EditableAccount
    let onDelete: () -> Void
    let onSave: () -> Void
    
    @State private var isHovered = false
    @State private var showingPassword = false
    
    var body: some View {
        HStack(spacing: 0) {
            // 序号列
            Text("\(account.index)")
                .font(.system(size: 13))
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .center)
                .padding(.vertical, 8)
            
            Divider()
                .frame(height: 30)
            
            // 名称列
            TextField("输入名称", text: $account.name)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    Rectangle()
                        .fill(account.name.isEmpty ? Color.red.opacity(0.1) : Color.clear)
                )
            
            Divider()
                .frame(height: 30)
            
            // 用户名列
            TextField("输入用户名", text: $account.username)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    Rectangle()
                        .fill(account.username.isEmpty ? Color.red.opacity(0.1) : Color.clear)
                )
            
            Divider()
                .frame(height: 30)
            
            // 密码列
            HStack(spacing: 4) {
                if showingPassword {
                    TextField("输入密码", text: $account.password)
                        .textFieldStyle(.plain)
                        .font(.system(size: 13))
                } else {
                    SecureField("输入密码", text: $account.password)
                        .textFieldStyle(.plain)
                        .font(.system(size: 13))
                }
                
                Button(action: { showingPassword.toggle() }) {
                    Image(systemName: showingPassword ? "eye.slash" : "eye")
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
                .opacity(isHovered ? 1.0 : 0.0)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Rectangle()
                    .fill(account.password.isEmpty ? Color.red.opacity(0.1) : Color.clear)
            )
            
            Divider()
                .frame(height: 30)
            
            // 操作列
            HStack(spacing: 4) {
                if account.hasChanges {
                    Button(action: onSave) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.green)
                    }
                    .buttonStyle(.plain)
                    .help("保存更改")
                    .disabled(!account.isValid)
                }
                
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .font(.system(size: 12))
                        .foregroundColor(.red)
                }
                .buttonStyle(.plain)
                .help("删除账号")
                .opacity(isHovered ? 1.0 : 0.3)
            }
            .frame(width: 80, alignment: .center)
            .padding(.vertical, 8)
        }
        .background(
            Rectangle()
                .fill(isHovered ? Color.gray.opacity(0.1) : Color.clear)
        )
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

#Preview {
    let account = EditableAccount(
        id: UUID(),
        index: 1,
        name: "测试账号",
        username: "<EMAIL>",
        password: "password123",
        originalAccount: nil
    )
    
    EditableAccountRowView(
        account: account,
        onDelete: {},
        onSave: {}
    )
    .frame(width: 800, height: 50)
}
