import SwiftData
import SwiftUI

struct EditAccountView: View {
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dismiss) private var dismiss

  let entry: PasswordEntry

  @State private var name = ""
  @State private var username = ""
  @State private var password = ""
  @State private var showingError = false
  @State private var errorMessage = ""

  var body: some View {
    VStack(spacing: 0) {
      // 头部工具栏
      HStack {
        Button("取消") {
          dismiss()
        }
        .buttonStyle(.plain)

        Spacer()

        Text("编辑账号")
          .font(.system(size: 16, weight: .semibold))

        Spacer()

        Button("保存") {
          saveChanges()
        }
        .buttonStyle(.plain)
        .disabled(name.isEmpty || username.isEmpty || password.isEmpty)
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 12)

      Divider()

      // 表单内容
      VStack(spacing: 20) {
        VStack(alignment: .leading, spacing: 16) {
          VStack(alignment: .leading, spacing: 8) {
            Text("账号名称")
              .font(.system(size: 14, weight: .medium))
            TextField("例如：GitHub、Gmail 等", text: $name)
              .textFieldStyle(.roundedBorder)
          }

          VStack(alignment: .leading, spacing: 8) {
            Text("用户名")
              .font(.system(size: 14, weight: .medium))
            TextField("邮箱或用户名", text: $username)
              .textFieldStyle(.roundedBorder)
          }

          VStack(alignment: .leading, spacing: 8) {
            Text("密码")
              .font(.system(size: 14, weight: .medium))
            SecureField("密码", text: $password)
              .textFieldStyle(.roundedBorder)
          }
        }
        .padding(.horizontal, 20)

        Spacer()
      }
      .padding(.top, 20)
    }
    .frame(width: 400, height: 300)
    .onAppear {
      name = entry.name
      username = entry.username
      password = entry.password
    }
    .alert("错误", isPresented: $showingError) {
      Button("确定") {}
    } message: {
      Text(errorMessage)
    }
  }

  private func saveChanges() {
    // 如果名称改变了，检查新名称是否已存在
    if name != entry.name {
      let descriptor = FetchDescriptor<PasswordEntry>(
        predicate: #Predicate { $0.name == name }
      )

      do {
        let existingEntries = try modelContext.fetch(descriptor)
        if !existingEntries.isEmpty {
          errorMessage = "账号名称已存在，请使用其他名称"
          showingError = true
          return
        }
      } catch {
        errorMessage = "检查失败：\(error.localizedDescription)"
        showingError = true
        return
      }
    }

    do {
      entry.name = name
      entry.username = username
      entry.password = password
      try modelContext.save()
      dismiss()
    } catch {
      errorMessage = "保存失败：\(error.localizedDescription)"
      showingError = true
    }
  }
}

#Preview {
  EditAccountView(
    entry: PasswordEntry(name: "示例账号", username: "<EMAIL>", password: "password123")
  )
  .modelContainer(for: PasswordEntry.self, inMemory: true)
}
