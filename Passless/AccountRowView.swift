import SwiftData
import <PERSON><PERSON>

struct AccountRowView: View {
  let entry: PasswordEntry
  @State private var isHovered = false
  @State private var showingInputOptions = false
  @ObservedObject private var pasteService = PasteService.shared

  var body: some View {
    Button(action: {
      // 默认使用直接输入方式
      pasteService.pasteCredentials(username: entry.username, password: entry.password)
    }) {
      HStack {
        VStack(alignment: .leading, spacing: 2) {
          Text(entry.name)
            .font(.system(size: 13, weight: .medium))
            .foregroundColor(.primary)
            .lineLimit(1)

          Text(entry.username)
            .font(.system(size: 11))
            .foregroundColor(.secondary)
            .lineLimit(1)
        }

        Spacer()

        if isHovered {
          Image(systemName: "doc.on.clipboard")
            .font(.system(size: 11))
            .foregroundColor(.secondary)
        }
      }
      .contentShape(Rectangle())
      .padding(.vertical, 6)
      .padding(.horizontal, 8)
    }
    .buttonStyle(.plain)
    .background(isHovered ? Color.blue.opacity(0.15) : Color.clear)
    .cornerRadius(6)
    .onHover { hover in
      withAnimation(.easeInOut(duration: 0.1)) {
        isHovered = hover
      }
    }
    .contextMenu {
      Button("自动填充") {
        pasteService.pasteCredentials(username: entry.username, password: entry.password)
      }

      Divider()

      Button("仅复制用户名") {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(entry.username, forType: .string)
        NotificationCenter.default.post(name: .closeStatusMenu, object: nil)
      }

      Button("仅复制密码") {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(entry.password, forType: .string)
        NotificationCenter.default.post(name: .closeStatusMenu, object: nil)
      }
    }
  }
}

#Preview {
  AccountRowView(
    entry: PasswordEntry(name: "示例账号", username: "<EMAIL>", password: "password123")
  )
  .padding()
}
