import SwiftUI
import SwiftData

struct AccountRowView: View {
    let entry: PasswordEntry
    @State private var isHovered = false
    @State private var showingInputOptions = false
    @ObservedObject private var pasteService = PasteService.shared

    var body: some View {
        Button(action: {
            // 默认使用直接输入方式
            pasteService.pasteCredentials(username: entry.username, password: entry.password)
        }) {
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 3) {
                    Text(entry.name)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(Color(NSColor.controlTextColor))
                        .lineLimit(1)
                        .truncationMode(.tail)

                    Text(entry.username)
                        .font(.system(size: 11))
                        .foregroundColor(Color(NSColor.placeholderTextColor))
                        .lineLimit(1)
                        .truncationMode(.tail)
                }

                Spacer()

                if isHovered {
                    Text("⌘1")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(Color(NSColor.placeholderTextColor))
                        .opacity(0.8)
                }
            }
            .contentShape(Rectangle())
            .padding(.vertical, 8)
            .padding(.horizontal, 16)
        }
        .buttonStyle(.plain)
        .background(
            Rectangle()
                .fill(isHovered ? Color(NSColor.selectedControlColor) : Color.clear)
        )
        .onHover { hover in
            withAnimation(.easeInOut(duration: 0.15)) {
                isHovered = hover
            }
        }
        .contextMenu {
            Button("自动填充") {
                pasteService.pasteCredentials(username: entry.username, password: entry.password)
            }

            Divider()

            Button("仅复制用户名") {
                let pasteboard = NSPasteboard.general
                pasteboard.clearContents()
                pasteboard.setString(entry.username, forType: .string)
                NotificationCenter.default.post(name: .closeStatusMenu, object: nil)
            }

            Button("仅复制密码") {
                let pasteboard = NSPasteboard.general
                pasteboard.clearContents()
                pasteboard.setString(entry.password, forType: .string)
                NotificationCenter.default.post(name: .closeStatusMenu, object: nil)
            }
        }
    }
}

#Preview {
    AccountRowView(entry: PasswordEntry(name: "示例账号", username: "<EMAIL>", password: "password123"))
        .padding()
}
