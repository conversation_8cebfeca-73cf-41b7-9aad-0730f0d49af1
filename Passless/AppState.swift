import SwiftUI
import SwiftData
import Foundation

@MainActor
class AppState: ObservableObject {
    @Published var searchText: String = ""
    @Published var accounts: [PasswordEntry] = []

    static let shared = AppState()

    private init() {
        loadAccounts()
    }

    func clearSearch() {
        searchText = ""
    }

    func addAccount(_ account: PasswordEntry) {
        accounts.append(account)
        saveAccounts()
    }

    func removeAccount(_ account: PasswordEntry) {
        accounts.removeAll { $0.id == account.id }
        saveAccounts()
    }

    func updateAccount(_ account: PasswordEntry) {
        if let index = accounts.firstIndex(where: { $0.id == account.id }) {
            accounts[index] = account
            saveAccounts()
        }
    }

    private func loadAccounts() {
        // 这里可以从SwiftData或其他持久化存储加载账号
        // 暂时使用示例数据
        accounts = []
    }

    private func saveAccounts() {
        // 这里可以保存到SwiftData或其他持久化存储
        // 暂时不做持久化
    }
}
