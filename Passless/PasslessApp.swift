import SwiftUI
import SwiftData
import AppKit

@main
struct PasslessApp: App {
    @State private var statusBarController: StatusBarController?
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @ObservedObject private var appState = AppState.shared
    
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            PasswordEntry.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        
        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()



    var body: some Scene {
        // 隐藏的菜单栏项，用于防止应用显示在 Dock 中
        MenuBarExtra("", systemImage: "lock.shield") {
            EmptyView()
        }
        .menuBarExtraStyle(.window)

        // 设置窗口
        WindowGroup("设置", id: "settings") {
            SettingsView()
                .modelContainer(sharedModelContainer)
        }
        .windowResizability(.contentSize)
        .defaultPosition(.center)
    }
    

}

// 扩展通知名称
extension Notification.Name {
    static let closeStatusMenu = Notification.Name("closeStatusMenu")
    static let openSettings = Notification.Name("openSettings")
}
